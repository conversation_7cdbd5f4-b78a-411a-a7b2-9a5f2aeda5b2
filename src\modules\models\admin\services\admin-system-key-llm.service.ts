import { AppException, ErrorCode } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { Injectable, Logger } from '@nestjs/common';
import { AiProviderHelper } from '@shared/services/ai/helpers/ai-provider.helper';
import { Transactional } from 'typeorm-transactional';
import { MODELS_ERROR_CODES } from '../../exceptions';
import { ApiKeyEncryptionHelper } from '../../helpers/api-key-encryption.helper';
import { SystemKeyLlmRepository } from '../../repositories/system-key-llm.repository';
import {
  CreateSystemKeyLlmDto,
  RestoreSystemKeyLlmResponseDto,
  SystemKeyLlmDetailResponseDto,
  SystemKeyLlmQueryDto,
  SystemKeyLlmResponseDto,
  TestConnectionResponseDto,
  UpdateSystemKeyLlmDto
} from '../dto/system-key-llm';
import { SystemKeyLlmMapper } from '../mappers/system-key-llm.mapper';

/**
 * Service xử lý business logic cho Admin System Key LLM
 */
@Injectable()
export class AdminSystemKeyLlmService {
  private readonly logger = new Logger(AdminSystemKeyLlmService.name);

  constructor(
    private readonly systemKeyLlmRepository: SystemKeyLlmRepository,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
    private readonly aiProviderHelper: AiProviderHelper,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly aiProviderService: AiProviderHelper,
  ) { }

  /**
   * Tạo mới system key LLM
   */
  @Transactional()
  async create(createDto: CreateSystemKeyLlmDto, employeeId: number): Promise<ApiResponseDto<{ id: string }>> {
    this.logger.log(`Creating system key LLM by employee ${employeeId}`);

    // Kiểm tra trùng tên
    const existsByName = await this.systemKeyLlmRepository.existsByName(createDto.name);
    if (existsByName) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NAME_EXISTS);
    }

    // Test connection trước khi lưu
    const encryptedApiKey = this.apiKeyEncryptionHelper.encryptAdminApiKey(createDto.apiKey);

    // Test connection với provider
    const testResult = await this.aiProviderService.testConnection(
      encryptedApiKey,
      createDto.provider,
      true,
    );

    if (!testResult.success) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_CONNECTION_FAILED, testResult.error);
    }

    // Tạo entity mới
    const newSystemKey = this.systemKeyLlmRepository.create({
      name: createDto.name,
      provider: createDto.provider,
      apiKey: encryptedApiKey,
      createdAt: Date.now(),
      createdBy: employeeId,
      updatedAt: Date.now(),
      updatedBy: employeeId
    });

    // Lưu vào database
    const savedKey = await this.systemKeyLlmRepository.save(newSystemKey);

    this.logger.log(`Created system key LLM ${savedKey.id} successfully`);
    return ApiResponseDto.success({ id: savedKey.id });
  }

  /**
   * Lấy danh sách system key LLM có phân trang
   */
  async findAll(queryDto: SystemKeyLlmQueryDto): Promise<ApiResponseDto<PaginatedResult<SystemKeyLlmResponseDto>>> {
    this.logger.log('Getting system key LLM list');

    // Lấy dữ liệu từ repository
    const result = await this.systemKeyLlmRepository.findWithPagination(queryDto);

    // Convert sang DTO với thông tin employee
    const items = SystemKeyLlmMapper.toResponseDtoArray(result.items);

    return ApiResponseDto.paginated({
      items,
      meta: result.meta
    });
  }

  /**
   * Lấy chi tiết system key LLM
   */
  async findOne(id: string): Promise<ApiResponseDto<SystemKeyLlmDetailResponseDto>> {
    this.logger.log(`Getting system key LLM detail: ${id}`);

    // Tìm system key với đầy đủ dữ liệu (không bao gồm encrypted API key)
    const systemKey = await this.systemKeyLlmRepository.createBaseQuery()
      .andWhere('systemKeyLlm.id = :id', { id })
      .getOne();

    if (!systemKey) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
    }

    // Lấy thông tin employee names
    const employeeIds: number[] = [];
    if (systemKey.createdBy) employeeIds.push(systemKey.createdBy);
    if (systemKey.updatedBy) employeeIds.push(systemKey.updatedBy);
    if (systemKey.deletedBy) employeeIds.push(systemKey.deletedBy);

    const employeeInfoMap = await this.employeeInfoService.getEmployeeInfoMap(employeeIds);

    const createdByInfo = systemKey.createdBy ? employeeInfoMap.get(systemKey.createdBy) || null : null;
    const updatedByInfo = systemKey.updatedBy ? employeeInfoMap.get(systemKey.updatedBy) || null : null;

    // Convert sang DTO
    const responseDto = SystemKeyLlmMapper.toDetailResponseDto(systemKey, createdByInfo, updatedByInfo);

    return ApiResponseDto.success(responseDto);
  }

  /**
   * Cập nhật system key LLM
   */
  @Transactional()
  async update(id: string, updateDto: UpdateSystemKeyLlmDto, employeeId: number): Promise<{ id: string }> {

    // Tìm system key hiện tại
    const existingKey = await this.systemKeyLlmRepository.findByIdWithFullData(id);
    if (!existingKey) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
    }

    // Kiểm tra trùng tên (nếu có thay đổi tên)
    if (updateDto.name && updateDto.name !== existingKey.name) {
      const existsByName = await this.systemKeyLlmRepository.existsByName(updateDto.name, id);
      if (existsByName) {
        throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NAME_EXISTS);
      }
    }

    // Cập nhật các trường
    if (updateDto.name !== undefined) {
      existingKey.name = updateDto.name;
    }

    if (updateDto.apiKey !== undefined) {
      // Test connection
      try {
        const tempEncryptedKey = this.apiKeyEncryptionHelper.encryptAdminApiKey(updateDto.apiKey);

        // Test connection với provider
        const testResult = await this.aiProviderService.testConnection(
          tempEncryptedKey,
          existingKey.provider,
          true,
        );

        if (!testResult.success) {
          throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_CONNECTION_FAILED, testResult.error);
        }

        existingKey.apiKey = tempEncryptedKey;
      } catch (error) {
        this.logger.error(`API key validation failed: ${error.message}`, error.stack);
        throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_INVALID_KEY);
      }
    }

    existingKey.updatedBy = employeeId;
    existingKey.updatedAt = Date.now();

    // Lưu thay đổi
    await this.systemKeyLlmRepository.update(id, existingKey);

    this.logger.log(`Updated system key LLM ${id} successfully`);
    return { id };
  }

  /**
   * Xóa system key LLM (soft delete)
   */
  @Transactional()
  async remove(id: string, employeeId: number): Promise<ApiResponseDto<{ id: string }>> {
    this.logger.log(`Soft deleting system key LLM ${id} by employee ${employeeId}`);

    // Kiểm tra system key tồn tại
    const existingKey = await this.systemKeyLlmRepository.findByIdWithFullData(id);
    if (!existingKey) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
    }

    // Thực hiện soft delete
    const deleted = await this.systemKeyLlmRepository.softDeleteSystemKey(id, employeeId);
    if (!deleted) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_DELETE_FAILED);
    }

    this.logger.log(`Soft deleted system key LLM ${id} successfully`);
    return ApiResponseDto.success({ id });
  }

  /**
   * Lấy danh sách system key LLM đã xóa
   */
  async findDeleted(queryDto: SystemKeyLlmQueryDto): Promise<ApiResponseDto<PaginatedResult<SystemKeyLlmResponseDto>>> {
    this.logger.log('Getting deleted system key LLM list');

    // Lấy dữ liệu từ repository
    const result = await this.systemKeyLlmRepository.findDeletedWithPagination(queryDto);

    // Convert sang DTO đơn giản (chỉ id, name, provider)
    const items = SystemKeyLlmMapper.toResponseDtoArray(result.items);

    return ApiResponseDto.paginated({
      items,
      meta: result.meta
    });
  }

  /**
   * Khôi phục system key LLM đã xóa
   */
  @Transactional()
  async restore(ids: string[], employeeId: number): Promise<ApiResponseDto<RestoreSystemKeyLlmResponseDto>> {
    this.logger.log(`Restoring system key LLM ${ids.join(', ')} by employee ${employeeId}`);

    // Kiểm tra input
    if (!ids || ids.length === 0) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_INVALID_KEY);
    }

    // Thực hiện khôi phục với kết quả chi tiết
    const result = await this.systemKeyLlmRepository.restoreWithDetails(ids);

    // Tạo response DTO
    const responseDto: RestoreSystemKeyLlmResponseDto = {
      restored: result.restored,
      failed: result.failed,
      totalRequested: ids.length,
      totalRestored: result.restored.length,
      totalFailed: result.failed.length
    };

    this.logger.log(`Restore completed: ${result.restored.length} restored, ${result.failed.length} failed`);
    return ApiResponseDto.success(responseDto);
  }

  /**
   * Test kết nối API key
   */
  async testConnection(id: string): Promise<ApiResponseDto<TestConnectionResponseDto>> {
    this.logger.log(`Testing connection for system key LLM ${id}`);

    // Tìm system key với encrypted API key
    const systemKey = await this.systemKeyLlmRepository.findByIdWithFullData(id);
    if (!systemKey) {
      throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
    }

    try {
      // Test connection bằng cách lấy danh sách models
      const result = await this.aiProviderHelper.testConnection(
        systemKey.provider,
        systemKey.apiKey,
      );

      if (result.success) {
        const responseDto = SystemKeyLlmMapper.toTestConnectionSuccessDto();
        return ApiResponseDto.success(responseDto);
      } else {
        const responseDto = SystemKeyLlmMapper.toTestConnectionFailureDto(
          result.error || 'Kết nối thất bại'
        );
        return ApiResponseDto.success(responseDto);
      }
    } catch (error) {
      this.logger.error(`Test connection failed for key ${id}: ${error.message}`, error.stack);

      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, error.message);
    }
  }
}
