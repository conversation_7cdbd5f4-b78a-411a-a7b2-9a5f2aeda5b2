import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserKeyLlm } from '../entities/user-key-llm.entity';
import { PaginatedResult } from '@common/response';
import { UserKeyLlmQueryDto } from '../user/dto/user-key-llm';

/**
 * Repository cho UserKeyLlm
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến user key LLM
 */
@Injectable()
export class UserKeyLlmRepository extends Repository<UserKeyLlm> {
  private readonly logger = new Logger(UserKeyLlmRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserKeyLlm, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserKeyLlm
   * @returns SelectQueryBuilder cho UserKeyLlm
   */
  createBaseQuery(): SelectQueryBuilder<UserKeyLlm> {
    return this.createQueryBuilder('ukl');
  }

  /**
   * Tìm user key LLM theo ID và user ID
   * @param id ID của user key LLM
   * @param userId ID của user
   * @returns UserKeyLlm hoặc null
   */
  async findByIdAndUserId(id: string, userId: number): Promise<UserKeyLlm | null> {
    return this.createBaseQuery()
      .andWhere('ukl.id = :id', { id })
      .andWhere('ukl.userId = :userId', { userId })
      .getOne();
  }

  /**
   * Kiểm tra user key LLM có tồn tại không
   * @param id ID của user key LLM
   * @param userId ID của user
   * @returns True nếu tồn tại
   */
  async isExistsByIdAndUserId(id: string, userId: number): Promise<boolean> {
    const count = await this.createBaseQuery()
      .andWhere('ukl.id = :id', { id })
      .andWhere('ukl.userId = :userId', { userId })
      .getCount();

    return count > 0;
  }

  /**
   * Kiểm tra tên key đã tồn tại chưa cho user
   * @param name Tên key
   * @param userId ID của user
   * @param excludeId ID cần loại trừ (cho update)
   * @returns True nếu đã tồn tại
   */
  async existsByNameAndUserId(name: string, userId: number, excludeId?: string): Promise<boolean> {
    const query = this.createQueryBuilder('ukl')
      .where('ukl.name = :name', { name })
      .andWhere('ukl.userId = :userId', { userId })
      .andWhere('ukl.deletedAt IS NULL');

    if (excludeId) {
      query.andWhere('ukl.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Tìm key mặc định theo provider và user
   * @param provider Provider
   * @param userId ID của user
   * @returns UserKeyLlm hoặc null
   */
  async findDefaultByProviderAndUserId(provider: string, userId: number): Promise<UserKeyLlm | null> {
    return this.createBaseQuery()
      .andWhere('ukl.provider = :provider', { provider })
      .andWhere('ukl.userId = :userId', { userId })
      .andWhere('ukl.isDefault = :isDefault', { isDefault: true })
      .getOne();
  }

  /**
   * Tìm user key LLM với phân trang
   * @param queryDto Query parameters
   * @param userId ID của user
   * @returns Kết quả phân trang
   */
  async findWithPagination(queryDto: UserKeyLlmQueryDto, userId: number): Promise<PaginatedResult<UserKeyLlm>> {
    const query = this.createBaseQuery()
      .andWhere('ukl.userId = :userId', { userId });

    // Tìm kiếm theo tên
    if (queryDto.search) {
      query.andWhere('(ukl.name ILIKE :search OR ukl.provider ILIKE :search)', {
        search: `%${queryDto.search}%`
      });
    }

    // Lọc theo provider
    if (queryDto.provider) {
      query.andWhere('ukl.provider = :provider', {
        provider: queryDto.provider
      });
    }

    // Sắp xếp
    if (queryDto.sortBy) {
      const direction = queryDto.sortDirection || 'ASC';
      query.orderBy(`ukl.${queryDto.sortBy}`, direction);
    } else {
      query.orderBy('ukl.created_at', 'DESC');
    }

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Tìm các user key LLM đã xóa với phân trang
   * @param queryDto DTO query
   * @param userId ID của user
   * @returns Kết quả phân trang
   */
  async findDeletedWithPagination(queryDto: UserKeyLlmQueryDto, userId: number): Promise<PaginatedResult<UserKeyLlm>> {
    const query = this.createQueryBuilder('ukl')
      .select([
        'ukl.id',
        'ukl.userId',
        'ukl.name',
        'ukl.provider',
        'ukl.baseUrl',
        'ukl.description',
        'ukl.status',
        'ukl.isDefault',
        'ukl.metadata',
        'ukl.createdAt',
        'ukl.updatedAt',
        'ukl.deletedAt'
      ])
      .where('ukl.deletedAt IS NOT NULL')
      .andWhere('ukl.userId = :userId', { userId });

    // Tìm kiếm theo tên
    if (queryDto.search) {
      query.andWhere('ukl.name ILIKE :search', {
        search: `%${queryDto.search}%`
      });
    }

    // Sắp xếp theo thời gian xóa
    query.orderBy('ukl.deletedAt', 'DESC');

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Soft delete user key LLM
   * @param id ID của user key LLM
   * @param userId ID của user
   * @returns true nếu thành công
   */
  async softDeleteUserKeyLlm(id: string, userId: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(UserKeyLlm)
      .set({
        deletedAt: Date.now()
      })
      .where('id = :id', { id })
      .andWhere('userId = :userId', { userId })
      .andWhere('deletedAt IS NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Khôi phục user key LLM đã xóa
   * @param id ID của user key LLM
   * @param userId ID của user
   * @returns true nếu thành công
   */
  async restoreUserKeyLlm(id: string, userId: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(UserKeyLlm)
      .set({
        deletedAt: null
      })
      .where('id = :id', { id })
      .andWhere('userId = :userId', { userId })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Tìm tất cả keys của user theo provider
   * @param provider Provider
   * @param userId ID của user
   * @returns Danh sách user key LLM
   */
  async findByProviderAndUserId(provider: string, userId: number): Promise<UserKeyLlm[]> {
    return this.createBaseQuery()
      .andWhere('ukl.provider = :provider', { provider })
      .andWhere('ukl.userId = :userId', { userId })
      .orderBy('ukl.isDefault', 'DESC')
      .addOrderBy('ukl.name', 'ASC')
      .getMany();
  }

  /**
   * Set key làm mặc định cho provider (unset others)
   * @param id ID của user key LLM
   * @param provider Provider
   * @param userId ID của user
   * @returns true nếu thành công
   */
  async setAsDefault(id: string, provider: string, userId: number): Promise<boolean> {
    const queryRunner = this.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Unset tất cả keys khác của provider này
      await queryRunner.manager
        .createQueryBuilder()
        .update(UserKeyLlm)
        .set({ isDefault: false })
        .where('provider = :provider', { provider })
        .andWhere('userId = :userId', { userId })
        .andWhere('deletedAt IS NULL')
        .execute();

      // Set key này làm default
      const result = await queryRunner.manager
        .createQueryBuilder()
        .update(UserKeyLlm)
        .set({ isDefault: true })
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId })
        .andWhere('deletedAt IS NULL')
        .execute();

      await queryRunner.commitTransaction();
      return (result.affected || 0) > 0;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Error setting default key:', error);
      return false;
    } finally {
      await queryRunner.release();
    }
  }
}
